#!/usr/bin/env python3

import os
import sys
import shutil
import hashlib

def error_exit(message):
    print(f"mygit-checkout: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if os.path.exists(branch_file):
        with open(branch_file, 'r') as f:
            commit_num = f.read().strip()
            return int(commit_num) if commit_num != '-1' else -1
    return -1

def get_file_hash(filename):
    if not os.path.exists(filename):
        return None
    with open(filename, 'rb') as f:
        content = f.read()
    return hashlib.sha1(content).hexdigest()

def get_commit_entries(commit_num):
    if commit_num == -1:
        return {}
    
    commit_index = f'.mygit/commits/{commit_num}/index'
    if not os.path.exists(commit_index):
        return {}
    
    entries = {}
    with open(commit_index, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    entries[parts[1]] = parts[0]
    return entries

def get_index_entries():
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]
    return index_entries

def check_overwrite_conflicts(target_commit):
    if target_commit == -1:
        return []

    target_entries = get_commit_entries(target_commit)
    index_entries = get_index_entries()
    conflicts = []

    for filename in target_entries:
        if os.path.exists(filename):
            current_hash = get_file_hash(filename)
            target_hash = target_entries[filename]
            index_hash = index_entries.get(filename)

            # 只有当文件在索引中且有未暂存的修改且目标版本会覆盖这些修改时才报冲突
            # 根据测试期望，如果文件不在索引中（未暂存），应该允许切换分支
            if (index_hash is not None and
                current_hash != index_hash and
                target_hash != index_hash and
                target_hash != current_hash):
                conflicts.append(filename)

    return conflicts

def should_preserve_working_copy(filename, target_hash, current_branch):
    """检查是否应该保持工作目录的文件版本"""
    if not os.path.exists(filename):
        return False

    current_hash = get_file_hash(filename)
    index_entries = get_index_entries()
    index_hash = index_entries.get(filename)

    # 获取当前分支的文件版本
    current_commit = get_branch_commit(current_branch)
    current_branch_entries = get_commit_entries(current_commit)
    current_branch_hash = current_branch_entries.get(filename)

    # 只有当以下条件都满足时才保持工作目录版本：
    # 1. 文件在索引中
    # 2. 工作目录与索引不同（有未暂存修改）
    # 3. 目标版本与当前分支版本相同（切换到相同内容的分支）
    if (index_hash is not None and
        current_hash != index_hash and
        target_hash == current_branch_hash):
        return True

    return False

def checkout_branch(branch_name):
    current_branch = get_current_branch()
    
    # 检查目标分支是否存在
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        error_exit(f"unknown branch '{branch_name}'")
    
    # 获取目标分支的提交号
    target_commit = get_branch_commit(branch_name)
    
    # 检查是否有文件会被覆盖
    conflicts = check_overwrite_conflicts(target_commit)
    if conflicts:
        print("mygit-checkout: error: Your changes to the following files would be overwritten by checkout:")
        for filename in sorted(conflicts):
            print(filename)
        return
    
    # 更新HEAD指向新分支
    with open('.mygit/HEAD', 'w') as f:
        f.write(branch_name + '\n')
    
    # 如果分支有提交，更新工作目录和索引
    if target_commit != -1:
        commit_index = f'.mygit/commits/{target_commit}/index'
        if os.path.exists(commit_index):
            # 复制提交的索引到当前索引
            shutil.copy2(commit_index, '.mygit/index')
            
            # 获取目标分支的文件
            target_entries = get_commit_entries(target_commit)
            
            # 删除不在目标分支中但在当前索引中的文件
            current_index = get_index_entries()
            for filename in current_index:
                if filename not in target_entries:
                    try:
                        if os.path.exists(filename):
                            os.remove(filename)
                    except:
                        pass
            
            # 更新工作目录中的文件
            for filename, file_hash in target_entries.items():
                object_path = f'.mygit/objects/{file_hash}'
                if os.path.exists(object_path):
                    # 检查是否应该保持工作目录版本
                    if should_preserve_working_copy(filename, file_hash, current_branch):
                        continue  # 保持工作目录的修改

                    shutil.copy2(object_path, filename)
    else:
        # 分支没有提交，清空索引但保留工作目录文件
        # 只删除在当前索引中的文件
        current_index = get_index_entries()
        with open('.mygit/index', 'w') as f:
            pass

        # 删除在索引中跟踪的文件
        for filename in current_index:
            try:
                if os.path.exists(filename):
                    os.remove(filename)
            except:
                pass
    
    print(f"Switched to branch '{branch_name}'")

def main():
    check_mygit_exists()
    
    if len(sys.argv) != 2:
        error_exit("usage: mygit-checkout branch-name")
    
    branch_name = sys.argv[1]
    checkout_branch(branch_name)

if __name__ == '__main__':
    main()
