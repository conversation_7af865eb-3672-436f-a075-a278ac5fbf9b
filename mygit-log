#!/usr/bin/env python3

import os
import sys

def error_exit(message):
    print(f"mygit-log: error: {message}", file=sys.stderr)
    sys.exit(1)
def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")
def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        return -1
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        return int(commit_num) if commit_num != '-1' else -1

def get_commit_message(commit_num):
    message_file = f'.mygit/commits/{commit_num}/message'
    if not os.path.exists(message_file):
        return ""
    
    with open(message_file, 'r') as f:
        return f.read().strip()

def build_commit_history():
    # 读取所有分支信息
    branches = {}
    if os.path.exists('.mygit/branches'):
        for branch_file in os.listdir('.mygit/branches'):
            if os.path.isfile(f'.mygit/branches/{branch_file}'):
                commit = get_branch_commit(branch_file)
                if commit != -1:
                    branches[branch_file] = commit

    # 构建提交的分支归属
    commit_branches = {}
    for branch, commit in branches.items():
        if commit not in commit_branches:
            commit_branches[commit] = []
        commit_branches[commit].append(branch)

    return commit_branches

def get_branch_history(branch_name):
    latest_commit = get_branch_commit(branch_name)
    if latest_commit == -1:
        return []

    # 构建分支的完整历史
    history = []

    if branch_name == 'trunk':
        # trunk分支：只显示trunk分支自己的提交历史
        # 需要排除其他分支的提交

        # 找出所有其他分支的提交
        other_branch_commits = set()
        if os.path.exists('.mygit/branches'):
            for branch_file in os.listdir('.mygit/branches'):
                if (branch_file != 'trunk' and
                    not branch_file.endswith('.info') and
                    os.path.isfile(f'.mygit/branches/{branch_file}')):
                    other_commit = get_branch_commit(branch_file)
                    if other_commit != -1:
                        other_branch_commits.add(other_commit)

        # trunk的历史：从最新提交开始，跳过其他分支的提交
        for i in range(latest_commit, -1, -1):
            if os.path.exists(f'.mygit/commits/{i}') and i not in other_branch_commits:
                history.append(i)
    else:
        # 其他分支：显示分支特有的提交和父分支历史
        # 读取分支创建信息
        branch_info_file = f'.mygit/branches/{branch_name}.info'
        parent_commit = -1
        if os.path.exists(branch_info_file):
            with open(branch_info_file, 'r') as f:
                content = f.read().strip()
                if content:
                    parent_commit = int(content)

        # 添加当前分支的提交
        history.append(latest_commit)

        # 如果有父提交，添加从父提交到根的历史
        if parent_commit != -1:
            for i in range(parent_commit, -1, -1):
                if os.path.exists(f'.mygit/commits/{i}'):
                    history.append(i)
        elif latest_commit > 0:
            # 如果没有分支信息文件，只显示根提交
            if os.path.exists('.mygit/commits/0'):
                history.append(0)

    return history

def main():
    check_mygit_exists()

    # 获取当前分支
    current_branch = get_current_branch()

    # 获取当前分支的最新提交
    latest_commit = get_branch_commit(current_branch)

    if latest_commit == -1:
        # 没有提交，不输出任何内容
        return

    # 获取分支历史
    history = get_branch_history(current_branch)

    # 显示历史
    for commit_num in history:
        if os.path.exists(f'.mygit/commits/{commit_num}'):
            message = get_commit_message(commit_num)
            print(f"{commit_num} {message}")

if __name__ == '__main__':
    main()
