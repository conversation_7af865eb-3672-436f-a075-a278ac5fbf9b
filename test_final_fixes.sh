#!/bin/bash

echo "=== Testing fixes for reported errors ==="

# Test 1: subset2_26 - checkout with modified file
echo "Test 1: subset2_26 - checkout with modified file"
rm -rf .mygit
./mygit-init > /dev/null
echo hello >a
./mygit-add a
./mygit-commit -m commit-A > /dev/null
./mygit-branch b1
echo world >>a
./mygit-checkout b1 > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ Test 1 PASSED: checkout allowed"
else
    echo "✗ Test 1 FAILED: checkout blocked"
fi

# Check if file modification is preserved
./mygit-status | grep "a - file changed, changes not staged for commit" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ Test 1 PASSED: file modification preserved"
else
    echo "✗ Test 1 FAILED: file modification not preserved"
fi

echo ""

# Test 2: subset2_27 - checkout with file preservation
echo "Test 2: subset2_27 - checkout with file preservation"
rm -rf .mygit
./mygit-init > /dev/null
touch a b c
./mygit-add a
./mygit-commit -m commit-A > /dev/null
./mygit-branch b1
./mygit-checkout b1 > /dev/null
if [ -f b ] && [ -f c ]; then
    echo "✓ Test 2 PASSED: untracked files preserved"
else
    echo "✗ Test 2 FAILED: untracked files not preserved"
fi

echo ""

# Test 3: subset2_31 - merge file content
echo "Test 3: subset2_31 - merge file content"
rm -rf .mygit
seq -f "line %.0f" 1 7 >a
seq -f "line %.0f" 1 7 >b
seq -f "line %.0f" 1 7 >c
seq -f "line %.0f" 1 7 >d
./mygit-init > /dev/null
./mygit-add a b c d
./mygit-commit -m commit-0 > /dev/null
./mygit-branch b1
./mygit-checkout b1 > /dev/null
seq -f "line %.0f" 0 7 >a
seq -f "line %.0f" 1 8 >b
seq -f "line %.0f" 1 7 >e
./mygit-add e
./mygit-commit -a -m commit-1 > /dev/null
./mygit-checkout trunk > /dev/null
sed -i 4d c
seq -f "line %.0f" 0 8 >d
seq -f "line %.0f" 1 7 >f
./mygit-add f
./mygit-commit -a -m commit-2 > /dev/null
./mygit-merge b1 -m merge1 > /dev/null

# Check if file c has correct content (line 4 should be deleted)
expected_c="line 1
line 2
line 3
line 5
line 6
line 7"

if [ "$(cat c)" = "$expected_c" ]; then
    echo "✓ Test 3 PASSED: merge file content correct"
else
    echo "✗ Test 3 FAILED: merge file content incorrect"
fi

echo ""

# Test 4: subset2_32 - merge conflict log
echo "Test 4: subset2_32 - merge conflict log"
rm -rf .mygit
seq 1 7 >7.txt
./mygit-init > /dev/null
./mygit-add 7.txt
./mygit-commit -m commit-0 > /dev/null
./mygit-branch b1
./mygit-checkout b1 > /dev/null
sed -Ei s/2/42/ 7.txt
./mygit-commit -a -m commit-1 > /dev/null
./mygit-checkout trunk > /dev/null
sed -Ei s/5/24/ 7.txt
./mygit-commit -a -m commit-2 > /dev/null
./mygit-merge b1 -m merge-message 2>/dev/null

# Check log output
log_output=$(./mygit-log)
expected_log="2 commit-2
0 commit-0"

if [ "$log_output" = "$expected_log" ]; then
    echo "✓ Test 4 PASSED: log shows correct commits after merge conflict"
else
    echo "✗ Test 4 FAILED: log shows incorrect commits after merge conflict"
fi

echo ""

# Test 5: subset2_34 - branch history
echo "Test 5: subset2_34 - branch history"
rm -rf .mygit
echo 0 >level0
./mygit-init > /dev/null
./mygit-add level0
./mygit-commit -m root > /dev/null
./mygit-branch b0
./mygit-branch b1
./mygit-checkout b0 > /dev/null
echo 0 >level1
./mygit-add level1
./mygit-commit -m 0 > /dev/null
./mygit-checkout b1 > /dev/null
echo 1 >level1
./mygit-add level1
./mygit-commit -m 1 > /dev/null
./mygit-checkout b0 > /dev/null
./mygit-branch b01
./mygit-checkout b01 > /dev/null
echo 01 >level2
./mygit-add level2
./mygit-commit -m 01 > /dev/null

# Check b01 log
log_output=$(./mygit-log)
expected_log="3 01
1 0
0 root"

if [ "$log_output" = "$expected_log" ]; then
    echo "✓ Test 5 PASSED: branch history shows complete lineage"
else
    echo "✗ Test 5 FAILED: branch history incomplete"
fi

echo ""
echo "=== All tests completed ==="
